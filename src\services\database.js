const Database = require('better-sqlite3');
const path = require('path');
const { app } = require('electron');

class DatabaseService {
  constructor() {
    this.db = null;
    this.init();
  }

  init() {
    try {
      // Tạo đường dẫn database trong userData directory
      const userDataPath = app.getPath('userData');
      const dbPath = path.join(userDataPath, 'projects.db');

      // Khởi tạo database
      this.db = new Database(dbPath);

      // Tạo bảng projects nếu chưa tồn tại
      this.createTables();

      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  createTables() {
    const createProjectsTable = `
      CREATE TABLE IF NOT EXISTS projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        icon TEXT DEFAULT 'Folder',
        url TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    try {
      this.db.exec(createProjectsTable);

      // Thêm dữ liệu mẫu nếu bảng trống
      this.seedDefaultData();
    } catch (error) {
      console.error('Failed to create tables:', error);
      throw error;
    }
  }

  seedDefaultData() {
    const count = this.db.prepare('SELECT COUNT(*) as count FROM projects').get();

    if (count.count === 0) {
      const defaultProjects = [
        {
          name: 'Design Engineering',
          description: 'UI/UX design and frontend development projects',
          icon: 'Folder',
          url: '#'
        },
        {
          name: 'Sales & Marketing',
          description: 'Marketing campaigns and sales materials',
          icon: 'Folder',
          url: '#'
        },
        {
          name: 'Travel',
          description: 'Travel planning and documentation',
          icon: 'Folder',
          url: '#'
        }
      ];

      const insertStmt = this.db.prepare(`
        INSERT INTO projects (name, description, icon, url)
        VALUES (?, ?, ?, ?)
      `);

      for (const project of defaultProjects) {
        insertStmt.run(project.name, project.description, project.icon, project.url);
      }

      console.log('Default projects seeded successfully');
    }
  }

  // CRUD Operations
  getAllProjects() {
    try {
      const stmt = this.db.prepare('SELECT * FROM projects ORDER BY created_at DESC');
      return stmt.all();
    } catch (error) {
      console.error('Failed to get all projects:', error);
      throw error;
    }
  }

  getProjectById(id) {
    try {
      const stmt = this.db.prepare('SELECT * FROM projects WHERE id = ?');
      return stmt.get(id);
    } catch (error) {
      console.error('Failed to get project by id:', error);
      throw error;
    }
  }

  createProject(projectData) {
    try {
      const { name, description, icon = 'Folder', url = '#' } = projectData;

      const stmt = this.db.prepare(`
        INSERT INTO projects (name, description, icon, url)
        VALUES (?, ?, ?, ?)
      `);

      const result = stmt.run(name, description, icon, url);
      return this.getProjectById(result.lastInsertRowid);
    } catch (error) {
      console.error('Failed to create project:', error);
      throw error;
    }
  }

  updateProject(id, projectData) {
    try {
      const { name, description, icon, url } = projectData;

      const stmt = this.db.prepare(`
        UPDATE projects
        SET name = ?, description = ?, icon = ?, url = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);

      const result = stmt.run(name, description, icon, url, id);

      if (result.changes === 0) {
        throw new Error('Project not found');
      }

      return this.getProjectById(id);
    } catch (error) {
      console.error('Failed to update project:', error);
      throw error;
    }
  }

  deleteProject(id) {
    try {
      const stmt = this.db.prepare('DELETE FROM projects WHERE id = ?');
      const result = stmt.run(id);

      if (result.changes === 0) {
        throw new Error('Project not found');
      }

      return { success: true, deletedId: id };
    } catch (error) {
      console.error('Failed to delete project:', error);
      throw error;
    }
  }

  duplicateProject(id) {
    try {
      const originalProject = this.getProjectById(id);

      if (!originalProject) {
        throw new Error('Project not found');
      }

      const duplicatedProject = {
        name: `${originalProject.name} (Copy)`,
        description: originalProject.description,
        icon: originalProject.icon,
        url: originalProject.url
      };

      return this.createProject(duplicatedProject);
    } catch (error) {
      console.error('Failed to duplicate project:', error);
      throw error;
    }
  }

  close() {
    if (this.db) {
      this.db.close();
      console.log('Database connection closed');
    }
  }
}

module.exports = DatabaseService;

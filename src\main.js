import { app, BrowserWindow, ipcMain } from 'electron';
import path from 'node:path';
import started from 'electron-squirrel-startup';

// <PERSON>le creating/removing shortcuts on Windows when installing/uninstalling.
if (started) {
  app.quit();
}

// Initialize database service
let dbService;

// Database service implementation
class DatabaseService {
  constructor() {
    this.db = null;
    this.init();
  }

  init() {
    try {
      // Dynamic import for better-sqlite3
      const Database = require('better-sqlite3');

      // Tạo đường dẫn database trong userData directory
      const userDataPath = app.getPath('userData');
      const dbPath = path.join(userDataPath, 'projects.db');

      // Khởi tạo database
      this.db = new Database(dbPath);

      // Tạo bảng projects nếu chưa tồn tại
      this.createTables();

      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  createTables() {
    const createProjectsTable = `
      CREATE TABLE IF NOT EXISTS projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        icon TEXT DEFAULT 'Folder',
        url TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    try {
      this.db.exec(createProjectsTable);

      // Thêm dữ liệu mẫu nếu bảng trống
      this.seedDefaultData();
    } catch (error) {
      console.error('Failed to create tables:', error);
      throw error;
    }
  }

  seedDefaultData() {
    const count = this.db.prepare('SELECT COUNT(*) as count FROM projects').get();

    if (count.count === 0) {
      const defaultProjects = [
        {
          name: 'Design Engineering',
          description: 'UI/UX design and frontend development projects',
          icon: 'Folder',
          url: '#'
        },
        {
          name: 'Sales & Marketing',
          description: 'Marketing campaigns and sales materials',
          icon: 'Folder',
          url: '#'
        },
        {
          name: 'Travel',
          description: 'Travel planning and documentation',
          icon: 'Folder',
          url: '#'
        }
      ];

      const insertStmt = this.db.prepare(`
        INSERT INTO projects (name, description, icon, url)
        VALUES (?, ?, ?, ?)
      `);

      for (const project of defaultProjects) {
        insertStmt.run(project.name, project.description, project.icon, project.url);
      }

      console.log('Default projects seeded successfully');
    }
  }

  // CRUD Operations
  getAllProjects() {
    try {
      const stmt = this.db.prepare('SELECT * FROM projects ORDER BY created_at DESC');
      return stmt.all();
    } catch (error) {
      console.error('Failed to get all projects:', error);
      throw error;
    }
  }

  getProjectById(id) {
    try {
      const stmt = this.db.prepare('SELECT * FROM projects WHERE id = ?');
      return stmt.get(id);
    } catch (error) {
      console.error('Failed to get project by id:', error);
      throw error;
    }
  }

  createProject(projectData) {
    try {
      const { name, description, icon = 'Folder', url = '#' } = projectData;

      const stmt = this.db.prepare(`
        INSERT INTO projects (name, description, icon, url)
        VALUES (?, ?, ?, ?)
      `);

      const result = stmt.run(name, description, icon, url);
      return this.getProjectById(result.lastInsertRowid);
    } catch (error) {
      console.error('Failed to create project:', error);
      throw error;
    }
  }

  updateProject(id, projectData) {
    try {
      const { name, description, icon, url } = projectData;

      const stmt = this.db.prepare(`
        UPDATE projects
        SET name = ?, description = ?, icon = ?, url = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);

      const result = stmt.run(name, description, icon, url, id);

      if (result.changes === 0) {
        throw new Error('Project not found');
      }

      return this.getProjectById(id);
    } catch (error) {
      console.error('Failed to update project:', error);
      throw error;
    }
  }

  deleteProject(id) {
    try {
      const stmt = this.db.prepare('DELETE FROM projects WHERE id = ?');
      const result = stmt.run(id);

      if (result.changes === 0) {
        throw new Error('Project not found');
      }

      return { success: true, deletedId: id };
    } catch (error) {
      console.error('Failed to delete project:', error);
      throw error;
    }
  }

  duplicateProject(id) {
    try {
      const originalProject = this.getProjectById(id);

      if (!originalProject) {
        throw new Error('Project not found');
      }

      const duplicatedProject = {
        name: `${originalProject.name} (Copy)`,
        description: originalProject.description,
        icon: originalProject.icon,
        url: originalProject.url
      };

      return this.createProject(duplicatedProject);
    } catch (error) {
      console.error('Failed to duplicate project:', error);
      throw error;
    }
  }

  close() {
    if (this.db) {
      this.db.close();
      console.log('Database connection closed');
    }
  }
}

const createWindow = () => {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1600,
    height: 900,
    autoHideMenuBar: true,
    titleBarStyle: 'hidden',
    titleBarOverlay: {
      color: '#fafafa',
    },
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
    },
  });

  // and load the index.html of the app.
  if (MAIN_WINDOW_VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(MAIN_WINDOW_VITE_DEV_SERVER_URL);
  } else {
    mainWindow.loadFile(path.join(__dirname, `../renderer/${MAIN_WINDOW_VITE_NAME}/index.html`));
  }

  // Open the DevTools.
  mainWindow.webContents.openDevTools();
};

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Initialize database
  dbService = new DatabaseService();

  // Setup IPC handlers
  setupIpcHandlers();

  createWindow();

  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    // Close database connection before quitting
    if (dbService) {
      dbService.close();
    }
    app.quit();
  }
});

// Setup IPC handlers for database operations
function setupIpcHandlers() {
  // Get all projects
  ipcMain.handle('projects:getAll', async () => {
    try {
      return await dbService.getAllProjects();
    } catch (error) {
      console.error('Error getting all projects:', error);
      throw error;
    }
  });

  // Get project by ID
  ipcMain.handle('projects:getById', async (event, id) => {
    try {
      return await dbService.getProjectById(id);
    } catch (error) {
      console.error('Error getting project by ID:', error);
      throw error;
    }
  });

  // Create project
  ipcMain.handle('projects:create', async (event, projectData) => {
    try {
      return await dbService.createProject(projectData);
    } catch (error) {
      console.error('Error creating project:', error);
      throw error;
    }
  });

  // Update project
  ipcMain.handle('projects:update', async (event, id, projectData) => {
    try {
      return await dbService.updateProject(id, projectData);
    } catch (error) {
      console.error('Error updating project:', error);
      throw error;
    }
  });

  // Delete project
  ipcMain.handle('projects:delete', async (event, id) => {
    try {
      return await dbService.deleteProject(id);
    } catch (error) {
      console.error('Error deleting project:', error);
      throw error;
    }
  });

  // Duplicate project
  ipcMain.handle('projects:duplicate', async (event, id) => {
    try {
      return await dbService.duplicateProject(id);
    } catch (error) {
      console.error('Error duplicating project:', error);
      throw error;
    }
  });
}

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and import them here.
